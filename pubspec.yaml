name: cryptocurrency_app
description: A new Flutter project.

# The following line prevents the package from being accidentally published to
# pub.dev using `pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
version: 1.0.0+1

environment:
  sdk: '>=2.12.0 <3.0.0'

dependencies:
  flutter:
    sdk: flutter
  cupertino_icons: ^1.0.0
  hooks_riverpod: 2.0.0-dev.3
  flutter_riverpod: 2.0.0-dev.3
  dio: ^4.0.0
  flutter_dotenv: ^5.0.0
  fl_chart: ^0.36.1
  settings_ui: ^2.0.2
  auto_size_text: ^3.0.0-nullsafety.0
  flutter_secure_storage: ^5.0.2
  easy_localization: ^3.0.0
  freezed_annotation:
  flutter_candlesticks:
    git: https://github.com/salvadordeveloper/flutter-candlesticks.git
  mockito: ^5.0.9

dev_dependencies:
  integration_test:
    sdk: flutter
  flutter_test:
    sdk: flutter
  http_mock_adapter: ^0.3.2
  build_runner:
  freezed:
  json_serializable:
  flutter_launcher_icons: ^0.9.0
  flutter_native_splash: ^1.1.8+4

flutter_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/icon/icon.png"
  image_path_ios: "assets/icon/icon_ios.png"

flutter_native_splash:
  color: "#3D3D3D"
  image: assets/icon/icon.png
  color_dark: "#131313"

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec
# The following section is specific to Flutter.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  assets:
    - assets/translations/
    - assets/icon/icon.png
    - .env
  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.
  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages
  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages

PODS:
  - Flutter (1.0.0)
  - flutter_secure_storage (3.3.1):
    - Flutter
  - integration_test (0.0.1):
    - Flutter
  - shared_preferences (0.0.1):
    - Flutter

DEPENDENCIES:
  - Flutter (from `Flutter`)
  - flutter_secure_storage (from `.symlinks/plugins/flutter_secure_storage/ios`)
  - integration_test (from `.symlinks/plugins/integration_test/ios`)
  - shared_preferences (from `.symlinks/plugins/shared_preferences/ios`)

EXTERNAL SOURCES:
  Flutter:
    :path: Flutter
  flutter_secure_storage:
    :path: ".symlinks/plugins/flutter_secure_storage/ios"
  integration_test:
    :path: ".symlinks/plugins/integration_test/ios"
  shared_preferences:
    :path: ".symlinks/plugins/shared_preferences/ios"

SPEC CHECKSUMS:
  Flutter: 50d75fe2f02b26cc09d224853bb45737f8b3214a
  flutter_secure_storage: 7953c38a04c3fdbb00571bcd87d8e3b5ceb9daec
  integration_test: a1e7d09bd98eca2fc37aefd79d4f41ad37bdbbe5
  shared_preferences: af6bfa751691cdc24be3045c43ec037377ada40d

PODFILE CHECKSUM: aafe91acc616949ddb318b77800a7f51bffa2a4c

COCOAPODS: 1.10.2

// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides

part of 'settings_details.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more informations: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
class _$SettingsDetailsTearOff {
  const _$SettingsDetailsTearOff();

  _SettingsDetails call(
      {required String currentLanguage,
      required String favoriteExchange,
      required String favoritePair,
      required String themeMode}) {
    return _SettingsDetails(
      currentLanguage: currentLanguage,
      favoriteExchange: favoriteExchange,
      favoritePair: favoritePair,
      themeMode: themeMode,
    );
  }
}

/// @nodoc
const $SettingsDetails = _$SettingsDetailsTearOff();

/// @nodoc
mixin _$SettingsDetails {
  String get currentLanguage => throw _privateConstructorUsedError;
  String get favoriteExchange => throw _privateConstructorUsedError;
  String get favoritePair => throw _privateConstructorUsedError;
  String get themeMode => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $SettingsDetailsCopyWith<SettingsDetails> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SettingsDetailsCopyWith<$Res> {
  factory $SettingsDetailsCopyWith(
          SettingsDetails value, $Res Function(SettingsDetails) then) =
      _$SettingsDetailsCopyWithImpl<$Res>;
  $Res call(
      {String currentLanguage,
      String favoriteExchange,
      String favoritePair,
      String themeMode});
}

/// @nodoc
class _$SettingsDetailsCopyWithImpl<$Res>
    implements $SettingsDetailsCopyWith<$Res> {
  _$SettingsDetailsCopyWithImpl(this._value, this._then);

  final SettingsDetails _value;
  // ignore: unused_field
  final $Res Function(SettingsDetails) _then;

  @override
  $Res call({
    Object? currentLanguage = freezed,
    Object? favoriteExchange = freezed,
    Object? favoritePair = freezed,
    Object? themeMode = freezed,
  }) {
    return _then(_value.copyWith(
      currentLanguage: currentLanguage == freezed
          ? _value.currentLanguage
          : currentLanguage // ignore: cast_nullable_to_non_nullable
              as String,
      favoriteExchange: favoriteExchange == freezed
          ? _value.favoriteExchange
          : favoriteExchange // ignore: cast_nullable_to_non_nullable
              as String,
      favoritePair: favoritePair == freezed
          ? _value.favoritePair
          : favoritePair // ignore: cast_nullable_to_non_nullable
              as String,
      themeMode: themeMode == freezed
          ? _value.themeMode
          : themeMode // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
abstract class _$SettingsDetailsCopyWith<$Res>
    implements $SettingsDetailsCopyWith<$Res> {
  factory _$SettingsDetailsCopyWith(
          _SettingsDetails value, $Res Function(_SettingsDetails) then) =
      __$SettingsDetailsCopyWithImpl<$Res>;
  @override
  $Res call(
      {String currentLanguage,
      String favoriteExchange,
      String favoritePair,
      String themeMode});
}

/// @nodoc
class __$SettingsDetailsCopyWithImpl<$Res>
    extends _$SettingsDetailsCopyWithImpl<$Res>
    implements _$SettingsDetailsCopyWith<$Res> {
  __$SettingsDetailsCopyWithImpl(
      _SettingsDetails _value, $Res Function(_SettingsDetails) _then)
      : super(_value, (v) => _then(v as _SettingsDetails));

  @override
  _SettingsDetails get _value => super._value as _SettingsDetails;

  @override
  $Res call({
    Object? currentLanguage = freezed,
    Object? favoriteExchange = freezed,
    Object? favoritePair = freezed,
    Object? themeMode = freezed,
  }) {
    return _then(_SettingsDetails(
      currentLanguage: currentLanguage == freezed
          ? _value.currentLanguage
          : currentLanguage // ignore: cast_nullable_to_non_nullable
              as String,
      favoriteExchange: favoriteExchange == freezed
          ? _value.favoriteExchange
          : favoriteExchange // ignore: cast_nullable_to_non_nullable
              as String,
      favoritePair: favoritePair == freezed
          ? _value.favoritePair
          : favoritePair // ignore: cast_nullable_to_non_nullable
              as String,
      themeMode: themeMode == freezed
          ? _value.themeMode
          : themeMode // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$_SettingsDetails implements _SettingsDetails {
  const _$_SettingsDetails(
      {required this.currentLanguage,
      required this.favoriteExchange,
      required this.favoritePair,
      required this.themeMode});

  @override
  final String currentLanguage;
  @override
  final String favoriteExchange;
  @override
  final String favoritePair;
  @override
  final String themeMode;

  @override
  String toString() {
    return 'SettingsDetails(currentLanguage: $currentLanguage, favoriteExchange: $favoriteExchange, favoritePair: $favoritePair, themeMode: $themeMode)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other is _SettingsDetails &&
            (identical(other.currentLanguage, currentLanguage) ||
                const DeepCollectionEquality()
                    .equals(other.currentLanguage, currentLanguage)) &&
            (identical(other.favoriteExchange, favoriteExchange) ||
                const DeepCollectionEquality()
                    .equals(other.favoriteExchange, favoriteExchange)) &&
            (identical(other.favoritePair, favoritePair) ||
                const DeepCollectionEquality()
                    .equals(other.favoritePair, favoritePair)) &&
            (identical(other.themeMode, themeMode) ||
                const DeepCollectionEquality()
                    .equals(other.themeMode, themeMode)));
  }

  @override
  int get hashCode =>
      runtimeType.hashCode ^
      const DeepCollectionEquality().hash(currentLanguage) ^
      const DeepCollectionEquality().hash(favoriteExchange) ^
      const DeepCollectionEquality().hash(favoritePair) ^
      const DeepCollectionEquality().hash(themeMode);

  @JsonKey(ignore: true)
  @override
  _$SettingsDetailsCopyWith<_SettingsDetails> get copyWith =>
      __$SettingsDetailsCopyWithImpl<_SettingsDetails>(this, _$identity);
}

abstract class _SettingsDetails implements SettingsDetails {
  const factory _SettingsDetails(
      {required String currentLanguage,
      required String favoriteExchange,
      required String favoritePair,
      required String themeMode}) = _$_SettingsDetails;

  @override
  String get currentLanguage => throw _privateConstructorUsedError;
  @override
  String get favoriteExchange => throw _privateConstructorUsedError;
  @override
  String get favoritePair => throw _privateConstructorUsedError;
  @override
  String get themeMode => throw _privateConstructorUsedError;
  @override
  @JsonKey(ignore: true)
  _$SettingsDetailsCopyWith<_SettingsDetails> get copyWith =>
      throw _privateConstructorUsedError;
}

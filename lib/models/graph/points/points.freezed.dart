// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides

part of 'points.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more informations: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
class _$PointsTearOff {
  const _$PointsTearOff();

  _Points call(
      {required double closeTime,
      required double openTime,
      required double highPrice,
      required double lowPrice,
      required double closePrice,
      required double volume,
      required double quoteVolume}) {
    return _Points(
      closeTime: closeTime,
      openTime: openTime,
      highPrice: highPrice,
      lowPrice: lowPrice,
      closePrice: closePrice,
      volume: volume,
      quoteVolume: quoteVolume,
    );
  }
}

/// @nodoc
const $Points = _$PointsTearOff();

/// @nodoc
mixin _$Points {
  double get closeTime => throw _privateConstructorUsedError;
  double get openTime => throw _privateConstructorUsedError;
  double get highPrice => throw _privateConstructorUsedError;
  double get lowPrice => throw _privateConstructorUsedError;
  double get closePrice => throw _privateConstructorUsedError;
  double get volume => throw _privateConstructorUsedError;
  double get quoteVolume => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $PointsCopyWith<Points> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PointsCopyWith<$Res> {
  factory $PointsCopyWith(Points value, $Res Function(Points) then) =
      _$PointsCopyWithImpl<$Res>;
  $Res call(
      {double closeTime,
      double openTime,
      double highPrice,
      double lowPrice,
      double closePrice,
      double volume,
      double quoteVolume});
}

/// @nodoc
class _$PointsCopyWithImpl<$Res> implements $PointsCopyWith<$Res> {
  _$PointsCopyWithImpl(this._value, this._then);

  final Points _value;
  // ignore: unused_field
  final $Res Function(Points) _then;

  @override
  $Res call({
    Object? closeTime = freezed,
    Object? openTime = freezed,
    Object? highPrice = freezed,
    Object? lowPrice = freezed,
    Object? closePrice = freezed,
    Object? volume = freezed,
    Object? quoteVolume = freezed,
  }) {
    return _then(_value.copyWith(
      closeTime: closeTime == freezed
          ? _value.closeTime
          : closeTime // ignore: cast_nullable_to_non_nullable
              as double,
      openTime: openTime == freezed
          ? _value.openTime
          : openTime // ignore: cast_nullable_to_non_nullable
              as double,
      highPrice: highPrice == freezed
          ? _value.highPrice
          : highPrice // ignore: cast_nullable_to_non_nullable
              as double,
      lowPrice: lowPrice == freezed
          ? _value.lowPrice
          : lowPrice // ignore: cast_nullable_to_non_nullable
              as double,
      closePrice: closePrice == freezed
          ? _value.closePrice
          : closePrice // ignore: cast_nullable_to_non_nullable
              as double,
      volume: volume == freezed
          ? _value.volume
          : volume // ignore: cast_nullable_to_non_nullable
              as double,
      quoteVolume: quoteVolume == freezed
          ? _value.quoteVolume
          : quoteVolume // ignore: cast_nullable_to_non_nullable
              as double,
    ));
  }
}

/// @nodoc
abstract class _$PointsCopyWith<$Res> implements $PointsCopyWith<$Res> {
  factory _$PointsCopyWith(_Points value, $Res Function(_Points) then) =
      __$PointsCopyWithImpl<$Res>;
  @override
  $Res call(
      {double closeTime,
      double openTime,
      double highPrice,
      double lowPrice,
      double closePrice,
      double volume,
      double quoteVolume});
}

/// @nodoc
class __$PointsCopyWithImpl<$Res> extends _$PointsCopyWithImpl<$Res>
    implements _$PointsCopyWith<$Res> {
  __$PointsCopyWithImpl(_Points _value, $Res Function(_Points) _then)
      : super(_value, (v) => _then(v as _Points));

  @override
  _Points get _value => super._value as _Points;

  @override
  $Res call({
    Object? closeTime = freezed,
    Object? openTime = freezed,
    Object? highPrice = freezed,
    Object? lowPrice = freezed,
    Object? closePrice = freezed,
    Object? volume = freezed,
    Object? quoteVolume = freezed,
  }) {
    return _then(_Points(
      closeTime: closeTime == freezed
          ? _value.closeTime
          : closeTime // ignore: cast_nullable_to_non_nullable
              as double,
      openTime: openTime == freezed
          ? _value.openTime
          : openTime // ignore: cast_nullable_to_non_nullable
              as double,
      highPrice: highPrice == freezed
          ? _value.highPrice
          : highPrice // ignore: cast_nullable_to_non_nullable
              as double,
      lowPrice: lowPrice == freezed
          ? _value.lowPrice
          : lowPrice // ignore: cast_nullable_to_non_nullable
              as double,
      closePrice: closePrice == freezed
          ? _value.closePrice
          : closePrice // ignore: cast_nullable_to_non_nullable
              as double,
      volume: volume == freezed
          ? _value.volume
          : volume // ignore: cast_nullable_to_non_nullable
              as double,
      quoteVolume: quoteVolume == freezed
          ? _value.quoteVolume
          : quoteVolume // ignore: cast_nullable_to_non_nullable
              as double,
    ));
  }
}

/// @nodoc

class _$_Points implements _Points {
  const _$_Points(
      {required this.closeTime,
      required this.openTime,
      required this.highPrice,
      required this.lowPrice,
      required this.closePrice,
      required this.volume,
      required this.quoteVolume});

  @override
  final double closeTime;
  @override
  final double openTime;
  @override
  final double highPrice;
  @override
  final double lowPrice;
  @override
  final double closePrice;
  @override
  final double volume;
  @override
  final double quoteVolume;

  @override
  String toString() {
    return 'Points(closeTime: $closeTime, openTime: $openTime, highPrice: $highPrice, lowPrice: $lowPrice, closePrice: $closePrice, volume: $volume, quoteVolume: $quoteVolume)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other is _Points &&
            (identical(other.closeTime, closeTime) ||
                const DeepCollectionEquality()
                    .equals(other.closeTime, closeTime)) &&
            (identical(other.openTime, openTime) ||
                const DeepCollectionEquality()
                    .equals(other.openTime, openTime)) &&
            (identical(other.highPrice, highPrice) ||
                const DeepCollectionEquality()
                    .equals(other.highPrice, highPrice)) &&
            (identical(other.lowPrice, lowPrice) ||
                const DeepCollectionEquality()
                    .equals(other.lowPrice, lowPrice)) &&
            (identical(other.closePrice, closePrice) ||
                const DeepCollectionEquality()
                    .equals(other.closePrice, closePrice)) &&
            (identical(other.volume, volume) ||
                const DeepCollectionEquality().equals(other.volume, volume)) &&
            (identical(other.quoteVolume, quoteVolume) ||
                const DeepCollectionEquality()
                    .equals(other.quoteVolume, quoteVolume)));
  }

  @override
  int get hashCode =>
      runtimeType.hashCode ^
      const DeepCollectionEquality().hash(closeTime) ^
      const DeepCollectionEquality().hash(openTime) ^
      const DeepCollectionEquality().hash(highPrice) ^
      const DeepCollectionEquality().hash(lowPrice) ^
      const DeepCollectionEquality().hash(closePrice) ^
      const DeepCollectionEquality().hash(volume) ^
      const DeepCollectionEquality().hash(quoteVolume);

  @JsonKey(ignore: true)
  @override
  _$PointsCopyWith<_Points> get copyWith =>
      __$PointsCopyWithImpl<_Points>(this, _$identity);
}

abstract class _Points implements Points {
  const factory _Points(
      {required double closeTime,
      required double openTime,
      required double highPrice,
      required double lowPrice,
      required double closePrice,
      required double volume,
      required double quoteVolume}) = _$_Points;

  @override
  double get closeTime => throw _privateConstructorUsedError;
  @override
  double get openTime => throw _privateConstructorUsedError;
  @override
  double get highPrice => throw _privateConstructorUsedError;
  @override
  double get lowPrice => throw _privateConstructorUsedError;
  @override
  double get closePrice => throw _privateConstructorUsedError;
  @override
  double get volume => throw _privateConstructorUsedError;
  @override
  double get quoteVolume => throw _privateConstructorUsedError;
  @override
  @JsonKey(ignore: true)
  _$PointsCopyWith<_Points> get copyWith => throw _privateConstructorUsedError;
}

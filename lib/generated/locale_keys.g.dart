// DO NOT EDIT. This is code generated via package:easy_localization/generate.dart

abstract class  LocaleKeys {
  static const homeTitle = 'homeTitle';
  static const openChart = 'openChart';
  static const searchTitle = 'searchTitle';
  static const searchBar = 'searchBar';
  static const noResults = 'noResults';
  static const settingsTitle = 'settingsTitle';
  static const languageSection = 'languageSection';
  static const language = 'language';
  static const dataSection = 'dataSection';
  static const exchange = 'exchange';
  static const topPair = 'topPair';
  static const designSection = 'designSection';
  static const appTheme = 'appTheme';
  static const spanish = 'spanish';
  static const english = 'english';
  static const summary = 'summary';
  static const orderbook = 'orderbook';
  static const trades = 'trades';
  static const ohlc = 'ohlc';
  static const price = 'price';
  static const last = 'last';
  static const high = 'high';
  static const low = 'low';
  static const change = 'change';
  static const volume = 'volume';
  static const quoteVolume = 'quoteVolume';
  static const time = 'time';
  static const amount = 'amount';
  static const bid = 'bid';
  static const ask = 'ask';
  static const errorRequestCancelled = 'errorRequestCancelled';
  static const errorConnectionTimeout = 'errorConnectionTimeout';
  static const errorInternetConnection = 'errorInternetConnection';
  static const errorReceiveTimeout = 'errorReceiveTimeout';
  static const errorSendTimeout = 'errorSendTimeout';
  static const errorBadRequest = 'errorBadRequest';
  static const errorRequestNotFound = 'errorRequestNotFound';
  static const errorIntenalServer = 'errorIntenalServer';
  static const errorSomethingWentWrong = 'errorSomethingWentWrong';

}
